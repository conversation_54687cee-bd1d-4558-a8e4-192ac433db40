/* <PERSON> Memorial Website - Optimized Styles */

/* ===== GOOGLE FONTS ===== */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Lora:wght@400;500;600&display=swap');

/* ===== CSS CUSTOM PROPERTIES ===== */
:root {
  /* Unified Green Color System */
  --forest-600: #2d5016;
  --forest-700: #1f3a0f;
  --forest-500: #3a6b1e;
  --forest-400: #4a7c2a;
  --forest-300: #5a8c3a;
  --stone-50: #fafafa;
  --stone-100: #f5f5f4;
  --stone-200: #e7e5e4;
  --stone-600: #57534e;
  --stone-700: #44403c;
  --stone-800: #292524;

  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: var(--stone-100);
  --bg-accent: var(--stone-50);

  /* Text Colors */
  --text-primary: var(--stone-800);
  --text-secondary: var(--stone-600);

  /* Brand Colors - Unified Green Palette */
  --brand-primary: var(--forest-600);
  --brand-secondary: var(--forest-500);
  --brand-accent: var(--forest-400);

  /* Spacing */
  --section-padding: 5rem 0;
  --container-max-width: 1200px;
  --container-padding: 0 1.5rem;
  --card-radius: 0.75rem;

  /* Typography */
  --font-primary: 'Lora', serif;
  --font-body: 'Inter', sans-serif;

  /* Shadows */
  --shadow-sm: 0 1px 3px rgba(0,0,0,0.05);
  --shadow-md: 0 4px 6px rgba(0,0,0,0.07);
  --shadow-lg: 0 10px 15px rgba(0,0,0,0.08);
}

/* ===== RESET & BASE STYLES ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-body);
  font-size: 1rem;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-primary);
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

h1 {
  font-size: 3rem;
  font-weight: 300;
}

h2 {
  font-size: 2.25rem;
  font-weight: 600;
  margin-bottom: 4rem;
}

h3 {
  font-size: 1.5rem;
  font-weight: 600;
}

h4 {
  font-size: 1.25rem;
  font-weight: 500;
}

/* Mobile Typography */
@media (max-width: 767px) {
  h1 {
    font-size: 2.5rem;
  }
  
  h2 {
    font-size: 2rem;
    margin-bottom: 2rem;
  }
  
  h3 {
    font-size: 1.25rem;
  }
}

/* ===== LAYOUT COMPONENTS ===== */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: var(--container-padding);
}

.section {
  padding: var(--section-padding);
}

@media (max-width: 767px) {
  .section {
    padding: 4rem 0;
  }
}

/* ===== UNIFIED COMPONENTS ===== */
.card {
  background: var(--bg-primary);
  border-radius: var(--card-radius);
  padding: 2rem;
  box-shadow: var(--shadow-lg);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-family: var(--font-body);
  font-weight: 500;
  font-size: 1rem;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background: var(--brand-primary);
  color: white;
}

.btn-primary:hover {
  background: var(--forest-700);
  transform: translateY(-2px);
}

.btn-secondary {
  background: var(--brand-secondary);
  color: white;
}

.btn-secondary:hover {
  background: var(--forest-700);
  transform: translateY(-2px);
}

/* ===== ANIMATIONS ===== */
.fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* ===== UTILITY CLASSES ===== */
.text-center {
  text-align: center;
}

.bg-secondary {
  background-color: var(--bg-secondary);
}

.bg-accent {
  background-color: var(--bg-accent);
}

.mb-1 { margin-bottom: 1rem; }
.mb-2 { margin-bottom: 2rem; }
.mb-3 { margin-bottom: 3rem; }
.mt-1 { margin-top: 1rem; }
.mt-2 { margin-top: 2rem; }
.mt-3 { margin-top: 3rem; }

/* ===== RESPONSIVE IMAGES ===== */
img {
  max-width: 100%;
  height: auto;
}

img[loading="lazy"] {
  opacity: 0;
  transition: opacity 0.3s;
}

img[loading="lazy"].loaded {
  opacity: 1;
}

/* ===== ACCESSIBILITY ===== */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus-visible:focus {
  outline: 2px solid var(--brand-primary);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card {
    border: 2px solid var(--text-primary);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .fade-in {
    opacity: 1;
    transform: none;
  }
}

/* ===== SECTION DIVIDERS ===== */
.section-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--stone-200), transparent);
  margin: 3rem 0;
}

/* ===== PROGRESS BAR ===== */
.progress-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 0%;
  height: 3px;
  background: linear-gradient(90deg, var(--brand-primary), var(--brand-accent));
  z-index: 1000;
  transition: width 0.1s ease;
}

/* ===== NAVIGATION ===== */
nav {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-sm);
  z-index: 50;
  transition: all 0.3s ease;
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
}

.nav-logo {
  font-family: var(--font-primary);
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  text-decoration: none;
}

.nav-menu {
  display: flex;
  gap: 2rem;
  list-style: none;
}

.nav-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
  color: var(--brand-primary);
}

/* Mobile Navigation */
.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 5px;
}

.hamburger span {
  width: 25px;
  height: 3px;
  background: var(--text-primary);
  margin: 3px 0;
  transition: 0.3s;
  border-radius: 2px;
}

.mobile-menu {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
  z-index: 999;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  transform: translateY(-100%);
  transition: transform 0.3s ease;
}

.mobile-menu.active {
  transform: translateY(0);
}

.mobile-menu a {
  font-size: 1.5rem;
  margin: 1rem 0;
  color: var(--text-primary);
  text-decoration: none;
  font-family: var(--font-primary);
  transition: color 0.3s ease;
}

.mobile-menu a:hover,
.mobile-menu a.active {
  color: var(--brand-primary);
}

@media (max-width: 768px) {
  .hamburger {
    display: flex;
  }

  .nav-menu {
    display: none;
  }

  .mobile-menu {
    display: flex;
  }
}

/* ===== HERO SECTION ===== */
.hero-bg {
  background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.3)),
              url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  position: relative;
  overflow: hidden;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
}

.hero-content {
  position: relative;
  z-index: 10;
  max-width: 4xl;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.hero-title {
  font-size: 4rem;
  font-weight: 300;
  margin-bottom: 1rem;
}

.hero-subtitle {
  font-size: 1.5rem;
  font-weight: 300;
  margin-bottom: 2rem;
}

.hero-description {
  font-size: 1.125rem;
  line-height: 1.75;
  max-width: 48rem;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.25rem;
  }

  .hero-description {
    font-size: 1rem;
  }
}

/* Scroll Indicator */
.scroll-indicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  cursor: pointer;
  animation: bounce 2s infinite;
  z-index: 10;
}

.scroll-indicator span {
  font-size: 0.9rem;
  margin-bottom: 10px;
  opacity: 0.8;
}

.scroll-arrow {
  width: 24px;
  height: 24px;
  border: 2px solid white;
  border-top: none;
  border-left: none;
  transform: rotate(45deg);
  opacity: 0.8;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

/* ===== TIMELINE STYLES ===== */
.timeline {
  position: relative;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(to bottom, var(--brand-primary), var(--brand-accent));
  transform: translateX(-50%);
}

.timeline-item {
  position: relative;
  margin: 3rem 0;
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s ease;
}

.timeline-item.animate {
  opacity: 1;
  transform: translateY(0);
}

.timeline-item:nth-child(odd) .timeline-content {
  left: 0;
  text-align: right;
  padding-right: 3rem;
}

.timeline-item:nth-child(even) .timeline-content {
  left: 50%;
  text-align: left;
  padding-left: 3rem;
}

.timeline-content {
  position: relative;
  width: 50%;
  background: var(--bg-primary);
  border-radius: var(--card-radius);
  padding: 2rem;
  box-shadow: var(--shadow-lg);
  cursor: pointer;
  transition: all 0.3s ease;
}

.timeline-content:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.timeline-dot {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 20px;
  height: 20px;
  background: linear-gradient(45deg, var(--brand-primary), var(--brand-accent));
  border-radius: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  box-shadow: 0 0 20px rgba(45, 80, 22, 0.5);
}

.timeline-dot::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 12px;
  height: 12px;
  background: white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

.content-photo {
  width: 100%;
  height: 180px;
  background-size: cover;
  background-position: center;
  border-radius: 8px;
  margin-bottom: 1rem;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.year {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--brand-secondary);
  margin-bottom: 0.5rem;
}

.phase {
  font-size: 1.125rem;
  color: var(--brand-primary);
  margin-bottom: 1rem;
  font-weight: 600;
}

.description {
  font-size: 1rem;
  line-height: 1.6;
  color: var(--text-secondary);
}

@media (max-width: 768px) {
  .timeline::before {
    left: 30px;
  }

  .timeline-item .timeline-content {
    width: calc(100% - 80px);
    left: 60px !important;
    text-align: left !important;
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important;
  }

  .timeline-dot {
    left: 30px;
  }
}

/* ===== GALLERY STYLES ===== */
.gallery-item {
  cursor: pointer;
  transition: transform 0.4s ease, filter 0.4s ease;
  position: relative;
  overflow: hidden;
  border-radius: var(--card-radius);
}

.gallery-item img {
  transition: transform 0.4s ease, filter 0.4s ease;
}

.gallery-item:hover img {
  transform: scale(1.05);
  filter: brightness(1.1);
}

/* ===== LIGHTBOX STYLES ===== */
.lightbox {
  backdrop-filter: blur(5px);
}

.lightbox.active {
  display: flex !important;
}

.lightbox-content img {
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

/* ===== AUDIO PLAYER STYLES ===== */
.audio-player {
  background: linear-gradient(135deg, rgba(58, 107, 30, 0.1), rgba(74, 124, 42, 0.1));
  border: 2px solid var(--forest-400);
}

.audio-controls button {
  background: var(--brand-secondary);
  color: white;
  border: none;
  transition: all 0.3s ease;
}

.audio-controls button:hover {
  background: var(--forest-700);
  transform: scale(1.05);
}

.volume-slider {
  width: 100px;
  height: 4px;
  background: rgba(45, 80, 22, 0.3);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
}

.volume-slider::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  background: var(--brand-secondary);
  border-radius: 50%;
  cursor: pointer;
}

.playlist-item {
  transition: background 0.3s ease;
}

.playlist-item:hover {
  background: rgba(45, 80, 22, 0.1);
}

.playlist-item.active {
  background: rgba(45, 80, 22, 0.2);
  border-left: 4px solid var(--brand-secondary);
}

/* ===== QUOTES CAROUSEL ===== */
.quotes-carousel {
  position: relative;
}

.quote-slide {
  display: none;
  animation: fadeInQuote 0.5s ease-in-out;
}

.quote-slide.active {
  display: block;
}

.carousel-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.5rem;
  margin-top: 2rem;
}

.carousel-btn {
  background: var(--brand-secondary);
  color: white;
  border: none;
  transition: all 0.3s ease;
}

.carousel-btn:hover {
  background: var(--forest-700);
  transform: scale(1.1);
}

.carousel-dot {
  transition: background 0.3s ease;
}

.carousel-dot.active {
  background: var(--brand-secondary) !important;
}

@keyframes fadeInQuote {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== GRID SYSTEM ===== */
.grid {
  display: grid;
  gap: var(--grid-gap);
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.col-span-2 {
  grid-column: span 2 / span 2;
}

.row-span-2 {
  grid-row: span 2 / span 2;
}

@media (max-width: 1023px) {
  .lg\\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

@media (max-width: 767px) {
  .md\\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .col-span-2,
  .row-span-2 {
    grid-column: span 1 / span 1;
    grid-row: span 1 / span 1;
  }
}
