/**
 * <PERSON> Website - Optimized JavaScript
 * Performance optimized with lazy loading and intersection observers
 */

// Global variables
let youtubePlayer;
let currentTrack = 0;
let isPlaying = false;
let currentQuoteSlide = 0;
let autoSlideInterval;

// YouTube playlist data
const youtubePlaylist = [
    {
        title: 'Banska Bystrica',
        artist: '<PERSON><PERSON>',
        videoId: 'Ywg2pvva-wg',
        duration: '4:12'
    },
    {
        title: 'Tam u nebeských bran',
        artist: '<PERSON><PERSON>',
        videoId: 'lRca7evReSs',
        duration: '3:45'
    },
    {
        title: '<PERSON> na kolej<PERSON>ch',
        artist: '<PERSON><PERSON>',
        videoId: 'Epkxt9kmTjE',
        duration: '5:23'
    }
];

// Airtable configuration
const AIRTABLE_CONFIG = {
    API_KEY: 'patHiJtaB2oEsFrqR.3bde9546beebc6c5b90174a149a3b2130e599e1dd63d50c0d2e125be72952cd<PERSON>',
    BASE_ID: 'appjrq70ohFM9hrvM',
    TABLE_ID: 'tblKbwEEyfqUXhfyh'
};

// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// Initialize all app functionality
function initializeApp() {
    initNavigation();
    initScrollEffects();
    initFadeInAnimations();
    initTimelineAnimations();
    initGalleryLightbox();
    initQuotesCarousel();
    initMemoryBook();
    initLazyLoading();
    
    // Lazy load heavy components
    setTimeout(() => {
        initYouTubePlayer();
        initMapbox();
    }, 1000);
}

// Navigation functionality
function initNavigation() {
    const hamburger = document.getElementById('hamburger');
    const mobileMenu = document.getElementById('mobileMenu');
    const navLinks = document.querySelectorAll('.nav-link, .mobile-nav-link');

    // Mobile menu toggle
    if (hamburger && mobileMenu) {
        hamburger.addEventListener('click', () => {
            hamburger.classList.toggle('active');
            mobileMenu.classList.toggle('active');
            
            const isExpanded = mobileMenu.classList.contains('active');
            hamburger.setAttribute('aria-expanded', isExpanded);
        });
    }

    // Smooth scrolling for navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetSection = document.getElementById(targetId);

            if (targetSection) {
                const headerHeight = 80;
                const targetPosition = targetSection.offsetTop - headerHeight;

                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });

                // Close mobile menu
                if (mobileMenu && mobileMenu.classList.contains('active')) {
                    mobileMenu.classList.remove('active');
                    hamburger.classList.remove('active');
                    hamburger.setAttribute('aria-expanded', 'false');
                }
            }
        });
    });

    // Active navigation state
    window.addEventListener('scroll', updateActiveNavigation);
}

// Update active navigation state
function updateActiveNavigation() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link, .mobile-nav-link');
    let currentSection = '';

    sections.forEach(section => {
        const sectionTop = section.offsetTop - 100;
        const sectionHeight = section.offsetHeight;

        if (window.pageYOffset >= sectionTop &&
            window.pageYOffset < sectionTop + sectionHeight) {
            currentSection = section.getAttribute('id');
        }
    });

    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === '#' + currentSection) {
            link.classList.add('active');
        }
    });
}

// Scroll effects
function initScrollEffects() {
    // Progress bar
    function updateProgressBar() {
        const scrollTop = window.pageYOffset;
        const docHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;
        const progressBar = document.getElementById('progressBar');
        if (progressBar) {
            progressBar.style.width = scrollPercent + '%';
        }
    }

    // Parallax effect for hero
    function updateParallax() {
        const scrolled = window.pageYOffset;
        const parallaxElement = document.querySelector('.hero-parallax');
        if (parallaxElement) {
            const speed = scrolled * 0.5;
            parallaxElement.style.transform = `translateY(${speed}px)`;
        }
    }

    // Throttled scroll handler
    let ticking = false;
    function handleScroll() {
        if (!ticking) {
            requestAnimationFrame(() => {
                updateProgressBar();
                updateParallax();
                updateActiveNavigation();
                ticking = false;
            });
            ticking = true;
        }
    }

    window.addEventListener('scroll', handleScroll);
}

// Fade in animations with intersection observer
function initFadeInAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const delay = (entry.target.getAttribute('data-delay') || 0) + 'ms';
                entry.target.style.transitionDelay = delay;
                entry.target.classList.add('visible');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observe all fade-in elements
    document.querySelectorAll('.fade-in').forEach((el, index) => {
        el.setAttribute('data-delay', index * 100);
        observer.observe(el);
    });

    // Initial fade-in for hero elements
    setTimeout(() => {
        document.querySelectorAll('.hero-bg .fade-in').forEach(el => {
            el.classList.add('visible');
        });
    }, 500);
}

// Timeline animations
function initTimelineAnimations() {
    const timelineItems = document.querySelectorAll('.timeline-item');

    const timelineObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate');
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '50px'
    });

    timelineItems.forEach(item => {
        timelineObserver.observe(item);
    });

    // Timeline content interactions
    const timelineContents = document.querySelectorAll('.timeline-content');
    timelineContents.forEach(content => {
        content.addEventListener('click', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
            this.style.boxShadow = '0 20px 40px rgba(45, 80, 22, 0.3)';

            setTimeout(() => {
                this.style.transform = '';
                this.style.boxShadow = '';
            }, 300);
        });
    });
}

// Scroll to next section
function scrollToNextSection() {
    const nextSection = document.querySelector('#zivot');
    if (nextSection) {
        nextSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// Lazy loading for images
function initLazyLoading() {
    const images = document.querySelectorAll('img[loading="lazy"]');
    
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.classList.add('loaded');
                    imageObserver.unobserve(img);
                }
            });
        });

        images.forEach(img => {
            imageObserver.observe(img);
        });
    }
}

// Error handling for missing images
function handleImageError(img) {
    img.style.display = 'none';
    console.warn('Image failed to load:', img.src);
}

// Utility function for debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Gallery lightbox functionality
function initGalleryLightbox() {
    const galleryItems = document.querySelectorAll('.gallery-item img');
    const lightbox = document.getElementById('lightbox');
    const lightboxImg = document.getElementById('lightboxImg');
    const lightboxClose = document.getElementById('lightboxClose');
    const lightboxPrev = document.getElementById('lightboxPrev');
    const lightboxNext = document.getElementById('lightboxNext');

    let currentImageIndex = 0;
    let images = [];

    // Create image list
    galleryItems.forEach((img, index) => {
        images.push({
            src: img.src,
            alt: img.alt
        });

        img.addEventListener('click', () => {
            currentImageIndex = index;
            showLightbox();
        });
    });

    function showLightbox() {
        if (images.length > 0) {
            lightboxImg.src = images[currentImageIndex].src;
            lightboxImg.alt = images[currentImageIndex].alt;
            lightbox.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    }

    function hideLightbox() {
        lightbox.classList.remove('active');
        document.body.style.overflow = '';
    }

    function showNextImage() {
        currentImageIndex = (currentImageIndex + 1) % images.length;
        lightboxImg.src = images[currentImageIndex].src;
        lightboxImg.alt = images[currentImageIndex].alt;
    }

    function showPrevImage() {
        currentImageIndex = (currentImageIndex - 1 + images.length) % images.length;
        lightboxImg.src = images[currentImageIndex].src;
        lightboxImg.alt = images[currentImageIndex].alt;
    }

    // Event listeners
    if (lightboxClose) lightboxClose.addEventListener('click', hideLightbox);
    if (lightboxNext) lightboxNext.addEventListener('click', showNextImage);
    if (lightboxPrev) lightboxPrev.addEventListener('click', showPrevImage);

    if (lightbox) {
        lightbox.addEventListener('click', (e) => {
            if (e.target === lightbox) {
                hideLightbox();
            }
        });
    }

    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
        if (lightbox && lightbox.classList.contains('active')) {
            switch(e.key) {
                case 'Escape':
                    hideLightbox();
                    break;
                case 'ArrowLeft':
                    showPrevImage();
                    break;
                case 'ArrowRight':
                    showNextImage();
                    break;
            }
        }
    });

    // Touch/swipe gestures for mobile
    let touchStartX = 0;
    let touchEndX = 0;

    if (lightbox) {
        lightbox.addEventListener('touchstart', (e) => {
            touchStartX = e.changedTouches[0].screenX;
        });

        lightbox.addEventListener('touchend', (e) => {
            touchEndX = e.changedTouches[0].screenX;
            handleSwipe();
        });
    }

    function handleSwipe() {
        const swipeThreshold = 50;
        const diff = touchStartX - touchEndX;

        if (Math.abs(diff) > swipeThreshold) {
            if (diff > 0) {
                showNextImage();
            } else {
                showPrevImage();
            }
        }
    }
}

// Quotes carousel functionality
function initQuotesCarousel() {
    const slides = document.querySelectorAll('.quote-slide');
    const indicators = document.querySelectorAll('.carousel-dot');
    const prevBtn = document.getElementById('prevQuote');
    const nextBtn = document.getElementById('nextQuote');
    const carousel = document.getElementById('quotesCarousel');

    if (!slides.length) return;

    function showSlide(index) {
        slides.forEach(slide => slide.classList.remove('active'));
        indicators.forEach(dot => dot.classList.remove('active'));

        slides[index].classList.add('active');
        if (indicators[index]) indicators[index].classList.add('active');

        currentQuoteSlide = index;
    }

    function nextSlide() {
        const next = (currentQuoteSlide + 1) % slides.length;
        showSlide(next);
    }

    function prevSlide() {
        const prev = (currentQuoteSlide - 1 + slides.length) % slides.length;
        showSlide(prev);
    }

    function startAutoSlide() {
        autoSlideInterval = setInterval(nextSlide, 5000);
    }

    function stopAutoSlide() {
        clearInterval(autoSlideInterval);
    }

    // Event listeners
    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            nextSlide();
            stopAutoSlide();
            startAutoSlide();
        });
    }

    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            prevSlide();
            stopAutoSlide();
            startAutoSlide();
        });
    }

    // Indicator clicks
    indicators.forEach((dot, index) => {
        dot.addEventListener('click', () => {
            showSlide(index);
            stopAutoSlide();
            startAutoSlide();
        });
    });

    // Pause auto-slide on hover
    if (carousel) {
        carousel.addEventListener('mouseenter', stopAutoSlide);
        carousel.addEventListener('mouseleave', startAutoSlide);
    }

    // Start auto-slide
    startAutoSlide();
}

// YouTube Player functionality (lazy loaded)
function initYouTubePlayer() {
    if (typeof YT === 'undefined' || !YT.Player) {
        console.warn('YouTube API not loaded, skipping player initialization');
        return;
    }

    const playPauseBtn = document.getElementById('playPauseBtn');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const currentTimeSpan = document.getElementById('currentTime');
    const totalTimeSpan = document.getElementById('totalTime');
    const volumeSlider = document.getElementById('volumeSlider');
    const trackTitle = document.getElementById('trackTitle');
    const playlistItems = document.querySelectorAll('.playlist-item');

    // Create YouTube player
    youtubePlayer = new YT.Player('youtube-player', {
        height: '200',
        width: '100%',
        videoId: youtubePlaylist[0].videoId,
        playerVars: {
            'playsinline': 1,
            'controls': 0,
            'rel': 0,
            'showinfo': 0,
            'modestbranding': 1
        },
        events: {
            'onReady': onPlayerReady,
            'onStateChange': onPlayerStateChange
        }
    });

    function onPlayerReady(event) {
        loadTrack(currentTrack);
        updateTimeDisplay();
    }

    function onPlayerStateChange(event) {
        if (event.data == YT.PlayerState.PLAYING) {
            isPlaying = true;
            if (playPauseBtn) playPauseBtn.textContent = '⏸';
            updateTimeDisplay();
        } else if (event.data == YT.PlayerState.PAUSED) {
            isPlaying = false;
            if (playPauseBtn) playPauseBtn.textContent = '▶';
        } else if (event.data == YT.PlayerState.ENDED) {
            nextTrack();
        }
    }

    function loadTrack(index) {
        const track = youtubePlaylist[index];
        if (youtubePlayer && youtubePlayer.loadVideoById) {
            youtubePlayer.loadVideoById(track.videoId);
            if (trackTitle) trackTitle.textContent = track.title;

            // Update playlist active state
            playlistItems.forEach((item, i) => {
                item.classList.toggle('active', i === index);
            });

            // Reset time display
            if (currentTimeSpan) currentTimeSpan.textContent = '0:00';
            if (totalTimeSpan) totalTimeSpan.textContent = track.duration;
        }
    }

    function togglePlayPause() {
        if (youtubePlayer) {
            if (isPlaying) {
                youtubePlayer.pauseVideo();
            } else {
                youtubePlayer.playVideo();
            }
        }
    }

    function previousTrack() {
        currentTrack = (currentTrack - 1 + youtubePlaylist.length) % youtubePlaylist.length;
        loadTrack(currentTrack);
    }

    function nextTrack() {
        currentTrack = (currentTrack + 1) % youtubePlaylist.length;
        loadTrack(currentTrack);
    }

    function formatTime(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    }

    function updateTimeDisplay() {
        if (youtubePlayer && youtubePlayer.getCurrentTime) {
            const currentTime = youtubePlayer.getCurrentTime();
            if (currentTimeSpan) currentTimeSpan.textContent = formatTime(currentTime);

            if (isPlaying) {
                setTimeout(updateTimeDisplay, 1000);
            }
        }
    }

    // Event listeners
    if (playPauseBtn) playPauseBtn.addEventListener('click', togglePlayPause);
    if (prevBtn) prevBtn.addEventListener('click', previousTrack);
    if (nextBtn) nextBtn.addEventListener('click', nextTrack);

    // Volume control
    if (volumeSlider) {
        volumeSlider.addEventListener('input', (e) => {
            if (youtubePlayer && youtubePlayer.setVolume) {
                youtubePlayer.setVolume(e.target.value);
            }
        });
    }

    // Playlist item clicks
    playlistItems.forEach((item, index) => {
        item.addEventListener('click', (e) => {
            if (!e.target.classList.contains('download-btn')) {
                currentTrack = index;
                loadTrack(currentTrack);
            }
        });
    });

    // Set initial volume
    if (youtubePlayer && youtubePlayer.setVolume) {
        youtubePlayer.setVolume(70);
    }
}

// Mapbox initialization (lazy loaded)
function initMapbox() {
    if (typeof mapboxgl === 'undefined') {
        console.warn('Mapbox GL not loaded, skipping map initialization');
        return;
    }

    mapboxgl.accessToken = 'pk.eyJ1IjoidGVzdC11c2VyIiwiYSI6ImNrZjBjMjJ1YjBjZjQyeW1xbWJkZGZkZGQifQ.test'; // Replace with actual token

    const map = new mapboxgl.Map({
        container: 'map',
        style: 'mapbox://styles/mapbox/outdoors-v12',
        center: [20.2, 49.1], // Vysoké Tatry coordinates
        zoom: 10
    });

    // Add markers for favorite places
    const places = [
        { name: 'Štrbské Pleso', coordinates: [20.0667, 49.1167] },
        { name: 'Skalnaté Pleso', coordinates: [20.2333, 49.1833] },
        { name: 'Tatranská Lomnica', coordinates: [20.2833, 49.1667] },
        { name: 'Popradské Pleso', coordinates: [20.1500, 49.1500] }
    ];

    places.forEach(place => {
        new mapboxgl.Marker({ color: '#2d5016' })
            .setLngLat(place.coordinates)
            .setPopup(new mapboxgl.Popup().setHTML(`<h3>${place.name}</h3>`))
            .addTo(map);
    });
}

// Memory book functionality with Airtable
function initMemoryBook() {
    const memoryForm = document.getElementById('memoryForm');
    const memoriesContainer = document.getElementById('memoriesContainer');
    const lightCandleBtn = document.getElementById('lightCandle');
    const candleNumber = document.getElementById('candleNumber');

    let candleCount = parseInt(localStorage.getItem('candleCount') || '127');
    if (candleNumber) candleNumber.textContent = candleCount;

    // Load existing memories
    loadMemoriesFromAirtable();

    // Add new memory
    if (memoryForm) {
        memoryForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const authorName = document.getElementById('authorName')?.value;
            const memoryText = document.getElementById('memoryText')?.value;

            if (authorName && memoryText) {
                try {
                    await saveMemoryToAirtable(authorName, memoryText);
                    addMemoryToPage(authorName, memoryText, 'práve teraz');
                    memoryForm.reset();
                    showSuccessMessage('Ďakujeme za vašu spomienku! Bola pridaná do knihy spomienok.');
                } catch (error) {
                    console.error('Error saving memory:', error);
                    showErrorMessage('Nastala chyba pri ukladaní spomienky. Skúste to znovu.');
                }
            }
        });
    }

    // Light candle functionality
    if (lightCandleBtn) {
        lightCandleBtn.addEventListener('click', function() {
            candleCount++;
            if (candleNumber) candleNumber.textContent = candleCount;
            localStorage.setItem('candleCount', candleCount.toString());

            // Animation
            this.style.transform = 'scale(1.1)';
            this.style.background = '#4a7c2a';

            setTimeout(() => {
                this.style.transform = '';
                this.style.background = '';
            }, 200);

            showSuccessMessage('✨ Sviečka zapálená');
        });
    }
}

// Airtable functions
async function saveMemoryToAirtable(meno, spomienka) {
    const response = await fetch(`https://api.airtable.com/v0/${AIRTABLE_CONFIG.BASE_ID}/${AIRTABLE_CONFIG.TABLE_ID}`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${AIRTABLE_CONFIG.API_KEY}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            fields: {
                "meno": meno,
                "spomienka": spomienka,
                "datum": new Date().toISOString().split('T')[0],
                "schvalene": true
            }
        })
    });

    if (!response.ok) {
        throw new Error(`Airtable API error: ${response.status}`);
    }

    return await response.json();
}

async function loadMemoriesFromAirtable() {
    try {
        const response = await fetch(`https://api.airtable.com/v0/${AIRTABLE_CONFIG.BASE_ID}/${AIRTABLE_CONFIG.TABLE_ID}?maxRecords=10`, {
            headers: {
                'Authorization': `Bearer ${AIRTABLE_CONFIG.API_KEY}`
            }
        });

        if (response.ok) {
            const data = await response.json();
            const memoriesContainer = document.getElementById('memoriesContainer');

            if (memoriesContainer) {
                memoriesContainer.innerHTML = '';

                data.records.forEach(record => {
                    const fields = record.fields;
                    const meno = fields.meno || 'Anonymný';
                    const spomienka = fields.spomienka || 'Bez textu';
                    const datum = fields.datum;

                    const timeAgo = datum ? formatTimeAgo(datum) : 'nedávno';
                    addMemoryToPage(meno, spomienka, timeAgo);
                });
            }
        }
    } catch (error) {
        console.error('Error loading memories:', error);
        showSampleMemories();
    }
}

function addMemoryToPage(name, text, timeAgo) {
    const memoriesContainer = document.getElementById('memoriesContainer');
    if (!memoriesContainer) return;

    const memoryDiv = document.createElement('div');
    memoryDiv.className = 'memory-item border-b border-gray-200 pb-4 mb-4';
    memoryDiv.innerHTML = `
        <div class="flex items-start gap-3">
            <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                <span class="text-forest-600 font-medium">${name.charAt(0).toUpperCase()}</span>
            </div>
            <div class="flex-1">
                <div class="flex items-center gap-2 mb-2">
                    <span class="font-medium text-gray-800">${name}</span>
                    <span class="text-sm text-gray-500">${timeAgo}</span>
                </div>
                <p class="text-gray-700 leading-relaxed">${text}</p>
            </div>
        </div>
    `;

    memoriesContainer.insertBefore(memoryDiv, memoriesContainer.firstChild);
}

function formatTimeAgo(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'včera';
    if (diffDays < 7) return `pred ${diffDays} dňami`;
    if (diffDays < 30) return `pred ${Math.ceil(diffDays / 7)} týždňami`;
    return `pred ${Math.ceil(diffDays / 30)} mesiacmi`;
}

function showSampleMemories() {
    const sampleMemories = [
        { name: 'Mária K.', text: 'Martin bol výnimočný človek. Vždy mal čas na rozhovor a jeho úsmev dokázal rozjasniť aj najtemnejší deň.', time: 'pred 2 hodinami' },
        { name: 'Peter S.', text: 'Spolu sme zdolali mnoho vrcholov. Martin ma naučil, že v horách nejde len o výkon, ale o to, aby sme si užili každý krok cesty.', time: 'pred 5 hodinami' },
        { name: 'Anna V.', text: 'Jeho hudba ma vždy dojala. Keď hral na gitare pri táboráku, čas sa zastavil. Ďakujem za všetky krásne chvíle.', time: 'včera' }
    ];

    sampleMemories.forEach(memory => {
        addMemoryToPage(memory.name, memory.text, memory.time);
    });
}

function showSuccessMessage(message) {
    const successDiv = document.createElement('div');
    successDiv.textContent = message;
    successDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #10b981;
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        z-index: 10000;
        max-width: 400px;
    `;

    document.body.appendChild(successDiv);

    setTimeout(() => {
        successDiv.remove();
    }, 3000);
}

function showErrorMessage(message) {
    const errorDiv = document.createElement('div');
    errorDiv.textContent = message;
    errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #ef4444;
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        z-index: 10000;
        max-width: 400px;
    `;

    document.body.appendChild(errorDiv);

    setTimeout(() => {
        errorDiv.remove();
    }, 5000);
}

// Export functions for global access
window.scrollToNextSection = scrollToNextSection;
window.handleImageError = handleImageError;
window.setupYouTubePlayer = initYouTubePlayer;
window.onYouTubeIframeAPIReady = initYouTubePlayer;
